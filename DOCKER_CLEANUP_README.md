# Docker清理功能优化说明

## 概述

为了提高脚本的模块化和可维护性，我们将 `docker-build-push-optimized.sh` 中的自动清理功能拆分到了独立的 `docker-cleanup.sh` 脚本中。

## 主要变更

### 1. 新增独立清理脚本 `docker-cleanup.sh`

**功能特性：**
- 轻量级清理：仅清理Docker构建缓存
- 完全清理：清理缓存、悬空镜像、未使用容器、网络、卷等
- 项目镜像清理：专门清理xiaozhi相关镜像
- 系统级清理：最彻底的清理（慎用）
- 指定镜像删除：删除特定的镜像

**使用方法：**
```bash
# 轻量级清理（默认）
./docker-cleanup.sh

# 完全清理
./docker-cleanup.sh full

# 清理xiaozhi相关镜像
./docker-cleanup.sh xiaozhi

# 预览模式（不实际执行）
./docker-cleanup.sh --dry-run full

# 自动确认模式
./docker-cleanup.sh full -y

# 删除指定镜像
./docker-cleanup.sh image xiaozhi-esp32-server:latest

# 查看帮助
./docker-cleanup.sh --help
```

### 2. 主脚本 `docker-build-push-optimized.sh` 的变更

**移除的功能：**
- `light_clean_docker_cache()` 函数
- `full_clean_docker_cache()` 函数
- `remove_local_image()` 函数
- `--cleanup` 命令行参数
- 构建前后的自动清理调用

**保留的功能：**
- `--skip-pre-clean` 参数（作为文档保留，不影响功能）
- 所有构建和推送功能完全保持不变

**新增的提示：**
- 在帮助信息中说明清理功能的新位置
- 在构建完成后提示可用的清理选项
- 在推送成功后建议使用清理脚本

## 优势

### 1. 模块化设计
- 清理功能独立，可以单独使用
- 主构建脚本逻辑更清晰
- 便于维护和扩展

### 2. 灵活性提升
- 用户可以根据需要选择清理时机
- 提供多种清理模式
- 支持预览模式，避免误操作

### 3. 向后兼容
- 主脚本的所有构建功能保持不变
- 用户可以继续使用原有的构建命令
- 清理功能通过独立脚本提供

## 使用建议

### 构建后清理
```bash
# 构建镜像
./docker-build-push-optimized.sh --server-only

# 构建完成后清理
./docker-cleanup.sh light  # 轻量级清理
# 或
./docker-cleanup.sh full   # 完全清理
```

### 定期维护
```bash
# 清理项目相关镜像
./docker-cleanup.sh xiaozhi -y

# 系统级清理（谨慎使用）
./docker-cleanup.sh system
```

### 预览操作
```bash
# 预览将要执行的清理操作
./docker-cleanup.sh --dry-run full
```

## 注意事项

1. **基础镜像保护**：默认情况下，清理脚本会保留 `*-base:amd64` 和 `*-base:arm64` 基础镜像，避免重复构建
2. **系统级清理**：`system` 模式会删除所有未使用的Docker资源，使用前请确认
3. **预览模式**：建议在执行重要清理操作前先使用 `--dry-run` 预览
4. **自动确认**：在自动化脚本中可以使用 `-y` 参数跳过确认提示

## 迁移指南

如果您之前使用了 `--cleanup` 参数，请改为：

**之前：**
```bash
./docker-build-push-optimized.sh --server-only --cleanup
```

**现在：**
```bash
./docker-build-push-optimized.sh --server-only
./docker-cleanup.sh full
```

这样的设计让您可以更灵活地控制清理时机和清理程度。
