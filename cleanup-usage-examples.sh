#!/bin/bash
# cleanup-usage-examples.sh
# 清理脚本使用示例

echo "=== Docker清理脚本使用示例 ==="
echo ""

echo "1. 查看帮助信息："
echo "   ./docker-cleanup.sh --help"
echo ""

echo "2. 轻量级清理（默认）："
echo "   ./docker-cleanup.sh"
echo "   ./docker-cleanup.sh light"
echo ""

echo "3. 完全清理："
echo "   ./docker-cleanup.sh full"
echo ""

echo "4. 清理xiaozhi相关镜像："
echo "   ./docker-cleanup.sh xiaozhi"
echo ""

echo "5. 预览模式（不实际执行）："
echo "   ./docker-cleanup.sh --dry-run full"
echo "   ./docker-cleanup.sh --dry-run xiaozhi"
echo ""

echo "6. 自动确认模式："
echo "   ./docker-cleanup.sh full -y"
echo "   ./docker-cleanup.sh xiaozhi -y"
echo ""

echo "7. 删除指定镜像："
echo "   ./docker-cleanup.sh image xiaozhi-esp32-server:latest"
echo ""

echo "8. 系统级清理（慎用）："
echo "   ./docker-cleanup.sh system"
echo ""

echo "9. 不保留基础镜像："
echo "   ./docker-cleanup.sh full --no-preserve-base"
echo ""

echo "=== 典型使用场景 ==="
echo ""

echo "构建后清理："
echo "   # 构建镜像"
echo "   ./docker-build-push-optimized.sh --server-only"
echo "   # 轻量级清理"
echo "   ./docker-cleanup.sh light"
echo ""

echo "开发环境维护："
echo "   # 清理项目镜像"
echo "   ./docker-cleanup.sh xiaozhi -y"
echo "   # 完全清理"
echo "   ./docker-cleanup.sh full -y"
echo ""

echo "预览操作："
echo "   # 预览将要删除的内容"
echo "   ./docker-cleanup.sh --dry-run xiaozhi"
echo "   # 确认后执行"
echo "   ./docker-cleanup.sh xiaozhi"
echo ""

echo "=== 注意事项 ==="
echo ""
echo "- 默认会保留 *-base:amd64 和 *-base:arm64 基础镜像"
echo "- system 模式会删除所有未使用的Docker资源，使用前请确认"
echo "- 建议在执行重要清理操作前先使用 --dry-run 预览"
echo "- 在自动化脚本中可以使用 -y 参数跳过确认提示"
echo ""
